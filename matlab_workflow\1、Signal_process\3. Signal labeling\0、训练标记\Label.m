%LABEL 肠鸣音信号自动标注函数（SB+MB+CRS检测版）
%   基于频谱分析自动检测肠鸣音信号中的SB（短脉冲音）、MB（多泡音）和CRS（连续性声音）事件。
%   该函数使用自适应阈值和多层过滤策略，实现对三种不同类型肠鸣音的精确识别和标注。
%
%   语法:
%   [labelVals, labelLocs] = label(x, t, parentLabelVal, parentLabelLoc)
%   [labelVals, labelLocs] = label(x, t, parentLabelVal, parentLabelLoc, Fs)
%
%   输入参数:
%   x               - 信号数据矩阵 (double array, N×M)
%                     N为采样点数，M为通道数
%   t               - 时间向量 (double array, N×1)
%                     对应信号数据的时间轴，单位：秒
%   parentLabelVal  - 父级标签值 (string/char)
%                     用于层次化标注的父级标签
%   parentLabelLoc  - 父级标签位置 (double array, 2×1)
%                     父级标签的时间范围 [起始时间, 结束时间]
%   Fs              - 采样频率 (double, 可选)
%                     信号采样率，默认值：2570 Hz
%
%   输出参数:
%   labelVals       - 事件类型标签数组 (string array, K×1)
%                     检测到的事件类型："SB"、"MB"或"CRS"
%   labelLocs       - 事件时间位置矩阵 (double array, K×2)
%                     每行为一个事件的时间范围 [起始时间, 结束时间]，单位：秒
%
%   检测标准:
%   SB (短脉冲音):
%     - 持续时间: 8-30ms
%     - 特征: 脉冲特征，高强度，经过密度过滤
%     - 阈值: 信号均值 + α×标准差 (α=0.08)
%
%   MB (多泡音):
%     - 组成: 2-5个SB事件的序列
%     - SB间隙: -8到100ms
%     - 总持续时间: 40-1500ms
%
%   CRS (连续性声音):
%     - 持续时间: 50-3000ms
%     - 特征: 连续性好，强度变化相对稳定
%     - 阈值: 信号均值 + α×标准差 (α=0.05，独立于SB阈值)
%
%   算法流程:
%   1. 计算信号频谱图并提取整个频段强度
%   2. 基于自适应阈值检测SB候选事件区域
%   3. 应用时间范围和强度筛选SB事件
%   4. 密度过滤去除过密集的事件
%   5. MB检测：寻找符合条件的SB序列组合
%   6. CRS检测：使用独立阈值检测连续声音
%   7. 处理事件重叠和替代关系，输出最终标注结果
%
%   示例:
%   % 基本用法
%   [labels, locations] = label(signalData, timeVector, "", []);
%
%   % 指定采样率
%   [labels, locations] = label(signalData, timeVector, "", [], 2570);
%
%   % 显示检测结果
%   for i = 1:length(labels)
%       fprintf('%s: %.3f-%.3f秒\n', labels(i), locations(i,1), locations(i,2));
%   end
%
%   注意事项:
%   - 所有可调参数都在函数内部的参数设置区域中定义
%   - 函数会自动处理多通道信号，但目前只处理第一个通道
%   - 检测阈值和时间范围可以通过修改函数内部参数进行调整
%   - CRS事件可以替代重叠的SB/MB事件
%
%   参见: AUDIO_PREPROCESSING, TEST_PREPROCESSING
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0
function [labelVals,labelLocs] = label(x,t,parentLabelVal,parentLabelLoc,varargin)

    % 初始化输出变量为空数组
    labelVals = string([]);  % 使用空的字符串数组
    labelLocs = zeros(0,2); % 改为0行2列的矩阵

    % ========== 可调参数设置区域 ==========
    alpha = 0.08;  % 阈值调节参数，控制检测敏感性（可自定义修改）
    % alpha = 1.5;  % 更敏感，检测更多弱事件
    % alpha = 2.0;  % 中等敏感
    % alpha = 2.5;  % 当前设置，中等偏严格
    % alpha = 3.0;  % 更严格，只检测明显事件
    
    % SB事件时间范围参数（单位：毫秒）
    min_duration_ms = 8;   % SB最短持续时间（可自定义修改）
    max_duration_ms = 30;  % SB最长持续时间（可自定义修改）
    % 参考范围建议：
    % min_duration_ms = 5;   max_duration_ms = 30;   % 更宽松，检测更多短事件
    % min_duration_ms = 8;   max_duration_ms = 35;   % 当前设置，标准范围
    % min_duration_ms = 10;  max_duration_ms = 30;   % 更严格，经典SB定义
    % min_duration_ms = 12;  max_duration_ms = 25;   % 最严格，高质量SB
    
    % 密度过滤参数
    window_size = 1;           % 密度检查窗口大小（秒）
    max_events_per_window = 5; % 每个窗口内允许的最大事件数
    % 参考设置建议：
    % window_size = 3; max_events_per_window = 5;   % 更严格的密度控制
    % window_size = 5; max_events_per_window = 8;   % 当前设置，中等控制
    % window_size = 10; max_events_per_window = 15; % 更宽松的密度控制
    
    % MB事件检测参数
    min_gap_ms = -8;    % MB内SB间最小间隙（毫秒）（可自定义修改）
    max_gap_ms = 100;  % MB内SB间最大间隙（毫秒）（可自定义修改）
    % 参考间隙建议：
    % min_gap_ms = 3;  max_gap_ms = 50;   % 更严格，经典MB定义
    % min_gap_ms = 5;  max_gap_ms = 100;  % 当前设置，更宽松
    % min_gap_ms = 5;  max_gap_ms = 150;  % 最宽松，检测更多MB
    
    min_sb_count = 2;   % MB内最少SB数量（可自定义修改）
    max_sb_count = 5;   % MB内最多SB数量（可自定义修改）
    % 参考数量建议：
    % min_sb_count = 2; max_sb_count = 4;  % 更严格
    % min_sb_count = 2; max_sb_count = 5;  % 当前设置，标准范围
    % min_sb_count = 2; max_sb_count = 8;  % 更宽松，检测复杂MB
    
    min_mb_duration_ms = 40;   % MB最短总持续时间（毫秒）（可自定义修改）
    max_mb_duration_ms = 1500; % MB最长总持续时间（毫秒）（可自定义修改）
    % 参考时长建议：
    % min_mb_duration_ms = 30;  max_mb_duration_ms = 1000;  % 更严格
    % min_mb_duration_ms = 40;  max_mb_duration_ms = 1500;  % 当前设置，标准范围
    % min_mb_duration_ms = 50;  max_mb_duration_ms = 2000;  % 更宽松
    
    % CRS事件检测参数（可自定义修改）
    crs_alpha = 0.05;            % CRS独立阈值调节参数（比SB更低，检测更多长时间事件）
    min_crs_duration_ms = 50;    % CRS最短持续时间（毫秒）
    max_crs_duration_ms = 3000;  % CRS最长持续时间（毫秒）
    % max_intensity_cv = 0.8;      % CRS最大强度变异系数（暂时注释）
    % max_silence_gap_ms = 20;     % CRS最大允许静默间隙（毫秒）（暂时注释）
    % 参考设置建议：
    % crs_alpha = 0.02;          % 更敏感，检测更多CRS事件
    % crs_alpha = 0.03;          % 当前设置，中等敏感
    % crs_alpha = 0.05;          % 更严格，只检测明显的CRS事件
    % min_crs_duration_ms = 40;  max_crs_duration_ms = 2000; % 更宽松
    % min_crs_duration_ms = 50;  max_crs_duration_ms = 3000; % 当前设置，标准范围
    % min_crs_duration_ms = 100; max_crs_duration_ms = 5000; % 更严格，长连续音
    % =====================================

    % 设置采样频率
    if nargin < 5
        Fs = 2570;
    else
        Fs = varargin{1};
    end

    % 显示输入信号的信息
    disp(['输入信号大小: ', num2str(size(x))]);
    disp(['采样率: ', num2str(Fs)]);
    disp(['阈值参数α: ', num2str(alpha)]);
    disp(['SB时间范围: ', num2str(min_duration_ms), '-', num2str(max_duration_ms), 'ms']);
    disp(['密度过滤: ', num2str(window_size), '秒窗口内最多', num2str(max_events_per_window), '个事件']);
    disp(['MB间隙范围: ', num2str(min_gap_ms), '-', num2str(max_gap_ms), 'ms']);
    disp(['MB序列长度: ', num2str(min_sb_count), '-', num2str(max_sb_count), '个SB']);
    disp(['MB总时长: ', num2str(min_mb_duration_ms), '-', num2str(max_mb_duration_ms), 'ms']);
    disp(['CRS阈值参数α: ', num2str(crs_alpha), ' (独立于SB阈值)']);
    disp(['CRS时间范围: ', num2str(min_crs_duration_ms), '-', num2str(max_crs_duration_ms), 'ms']);
    % disp(['CRS连续性要求: 变异系数<', num2str(max_intensity_cv), ', 静默间隙<', num2str(max_silence_gap_ms), 'ms']); % 暂时注释

    % SB肠鸣音检测逻辑
    for kj = 1:size(x,2)
        % 计算频谱图
        w = 0.01; % 设置窗口宽度（秒）
        win = round(Fs*w); % 计算窗口大小（样本数）
        ov = round(Fs*w*0.9); % 计算重叠部分（样本数）
        nfft = round(Fs*0.5); % 设置FFT点数
        [S_C, F_C, T_C, P_C] = spectrogram(x(:,kj), win, ov, nfft, Fs);

        % 计算声音强度（使用整个频谱，不限制频率范围）
        I_BowelSound = sum(P_C, 1);  % 对整个频谱求和

        % 显示频率和强度信息
        disp(['频率范围: ', num2str(F_C(1)), 'Hz 到 ', num2str(F_C(end)), 'Hz']);
        disp(['最大强度: ', num2str(max(I_BowelSound))]);
        disp(['最小强度: ', num2str(min(I_BowelSound))]);

        % 【SB专用】改进的自适应阈值设置
        signal_std = std(I_BowelSound);
        signal_mean = mean(I_BowelSound);
        
        % 使用可调参数α设置阈值
        threshold = signal_mean + alpha * signal_std;
        disp(['SB检测阈值: ', num2str(threshold)]);
        disp(['信号均值: ', num2str(signal_mean), '，标准差: ', num2str(signal_std)]);

        % 找到大于阈值的区域
        above_threshold = I_BowelSound > threshold;

        % 找到上升沿和下降沿
        rising_edges = find(diff([0, above_threshold, 0]) == 1);
        falling_edges = find(diff([0, above_threshold, 0]) == -1) - 1;

        % 计算3ms对应的样本数（边界延伸）
        extension_samples = round(0.003 * Fs);

        % 将每个事件的边界向前后延伸3ms
        extended_rising_edges = max(1, rising_edges - extension_samples);
        extended_falling_edges = min(length(T_C), falling_edges + extension_samples);

        % 初始化SB事件数组
        num_events = length(rising_edges);
        sb_events = [];  % 存储有效的SB事件信息
        
        % 【SB专用检测】精确的SB检测和验证逻辑
        for i = 1:num_events
            % 计算事件持续时间（毫秒），使用延伸后的边界
            duration_ms = (T_C(extended_falling_edges(i)) - T_C(extended_rising_edges(i))) * 1000;

            % 计算事件的平均强度和峰值强度，使用原始边界
            event_range = rising_edges(i):falling_edges(i);
            event_avg_intensity = mean(I_BowelSound(event_range));
            event_peak_intensity = max(I_BowelSound(event_range));

            % 记录事件的起止时间，使用延伸后的边界
            event_start = T_C(extended_rising_edges(i));
            event_end = T_C(extended_falling_edges(i));

            % 【SB检测条件】使用时间范围+峰值强度筛选，然后进行密度过滤
            if duration_ms >= min_duration_ms && duration_ms <= max_duration_ms  % 第1层：时间范围筛选
                % 第2层：峰值强度筛选
                if event_peak_intensity > threshold * 1.5  % 峰值要求更高
                    % 通过时间和强度双重筛选，记录SB事件（跳过形态和SNR验证）
                    sb_event.start_time = event_start;
                    sb_event.end_time = event_end;
                    sb_event.duration = duration_ms;
                    sb_event.peak_intensity = event_peak_intensity;
                    sb_event.avg_intensity = event_avg_intensity;
                    sb_event.snr = NaN;  % 暂不计算SNR
                    sb_event.rising_rate = NaN;  % 暂不计算上升率
                    sb_event.falling_rate = NaN;  % 暂不计算下降率
                    sb_event.component_count = 1;  % SB事件只包含1个组件
                    
                    sb_events = [sb_events; sb_event];
                    
                    % 简化输出：只显示SB事件编号和时间范围
                    fprintf('SB候选-%d: %.3fs-%.3fs (%.1fms)\n', ...
                        length(sb_events), event_start, event_end, duration_ms);
                end
            end
        end

        % 【第5层：密度过滤】移除过于密集的SB事件
        if ~isempty(sb_events)
            % 按时间排序
            [~, sort_idx] = sort([sb_events.start_time]);
            sb_events = sb_events(sort_idx);
            
            % 记录原始事件数量
            original_count = length(sb_events);
            
            % 使用全局密度过滤参数
            filtered_events = [];
            
            for i = 1:length(sb_events)
                current_time = sb_events(i).start_time;
                
                % 计算当前时间窗口内的事件数量
                window_start = current_time;
                window_end = current_time + window_size;
                
                events_in_window = 0;
                for j = 1:length(sb_events)
                    if sb_events(j).start_time >= window_start && sb_events(j).start_time <= window_end
                        events_in_window = events_in_window + 1;
                    end
                end
                
                % 如果密度不过高，保留此事件
                if events_in_window <= max_events_per_window
                    filtered_events = [filtered_events; sb_events(i)];
                end
            end
            
            sb_events = filtered_events;
            
            % 显示密度过滤结果
            if length(filtered_events) < original_count
                disp(['密度过滤: ', num2str(original_count), '→', num2str(length(filtered_events)), '个SB候选']);
            else
                disp(['SB候选事件: ', num2str(length(filtered_events)), '个']);
            end
        end

        % 【MB检测逻辑】在SB检测完成后，寻找MB序列
        final_events = sb_events;  % 初始化最终事件列表
        
        if ~isempty(sb_events)
            % 按时间排序确保顺序正确
            [~, sort_idx] = sort([sb_events.start_time]);
            sorted_sb_events = sb_events(sort_idx);
            
            % 初始化事件类型标记
            event_types = repmat({'SB'}, length(sorted_sb_events), 1);
            mb_detected = false;
            
            disp(['=== MB检测：尝试从', num2str(length(sorted_sb_events)), '个SB中寻找MB序列 ===']);
            
            % MB检测：寻找时间相近但有间隙的SB序列
            i = 1;
            while i <= length(sorted_sb_events)
                if strcmp(event_types{i}, 'SB')
                    % 寻找后续的SB事件
                    mb_sequence = i;
                    j = i + 1;
                    
                    while j <= length(sorted_sb_events) && strcmp(event_types{j}, 'SB')
                        % 计算间隙时间（毫秒）
                        gap_time_ms = (sorted_sb_events(j).start_time - sorted_sb_events(mb_sequence(end)).end_time) * 1000;
                        
                        % 间隙应该在设定范围内（短暂不规则间隙）
                        if gap_time_ms >= min_gap_ms && gap_time_ms <= max_gap_ms
                            mb_sequence = [mb_sequence, j];
                            disp(['  扩展MB序列: 添加SB-', num2str(j), ' (间隙=', num2str(gap_time_ms, '%.1f'), 'ms)']);
                        else
                            if gap_time_ms < min_gap_ms
                                disp(['  停止扩展: 到SB-', num2str(j), '间隙过小(', num2str(gap_time_ms, '%.1f'), 'ms<', num2str(min_gap_ms), 'ms)']);
                            else
                                disp(['  停止扩展: 到SB-', num2str(j), '间隙过大(', num2str(gap_time_ms, '%.1f'), 'ms>', num2str(max_gap_ms), 'ms)']);
                            end
                            break;  % 间隙不符合要求，停止序列
                        end
                        j = j + 1;
                    end
                    
                    % 如果找到指定数量SB组成的序列
                    if length(mb_sequence) >= min_sb_count && length(mb_sequence) <= max_sb_count
                        % 验证总持续时间是否在设定范围内
                        total_duration_ms = (sorted_sb_events(mb_sequence(end)).end_time - sorted_sb_events(mb_sequence(1)).start_time) * 1000;
                        
                        if total_duration_ms >= min_mb_duration_ms && total_duration_ms <= max_mb_duration_ms
                            % 将序列标记为MB组件
                            for idx = mb_sequence
                                event_types{idx} = 'MB_component';
                            end
                            mb_detected = true;
                            disp(['✓ 形成MB序列：SB-', num2str(mb_sequence), ' (', num2str(total_duration_ms, '%.1f'), 'ms, ', num2str(sorted_sb_events(mb_sequence(1)).start_time, '%.3f'), 's-', num2str(sorted_sb_events(mb_sequence(end)).end_time, '%.3f'), 's)']);
                        else
                            disp(['✗ 序列时长不符合: ', num2str(total_duration_ms, '%.1f'), 'ms (需要', num2str(min_mb_duration_ms), '-', num2str(max_mb_duration_ms), 'ms)']);
                        end
                    elseif length(mb_sequence) > 1
                        disp(['✗ SB数量不符合: ', num2str(length(mb_sequence)), '个 (需要', num2str(min_sb_count), '-', num2str(max_sb_count), '个)']);
                    end
                    
                    i = j;  % 跳过已处理的事件
                else
                    i = i + 1;
                end
            end
            
            % 重新组织最终事件列表
            final_events = [];
            final_labels = {};
            final_locations = [];
            
            % 处理MB序列：将MB组件合并为单个MB事件
            i = 1;
            while i <= length(sorted_sb_events)
                if strcmp(event_types{i}, 'MB_component')
                    % 找到MB序列的所有组件
                    mb_start = i;
                    while i <= length(sorted_sb_events) && strcmp(event_types{i}, 'MB_component')
                        i = i + 1;
                    end
                    mb_end = i - 1;
                    
                    % 创建单个MB事件
                    mb_event.start_time = sorted_sb_events(mb_start).start_time;
                    mb_event.end_time = sorted_sb_events(mb_end).end_time;
                    mb_event.duration = (mb_event.end_time - mb_event.start_time) * 1000;
                    
                    % 计算MB的强度特征（所有组件的平均）
                    mb_components = sorted_sb_events(mb_start:mb_end);
                    mb_event.peak_intensity = max([mb_components.peak_intensity]);
                    mb_event.avg_intensity = mean([mb_components.avg_intensity]);
                    mb_event.snr = NaN;
                    mb_event.rising_rate = NaN;  % 保持与SB字段一致
                    mb_event.falling_rate = NaN; % 保持与SB字段一致
                    mb_event.component_count = mb_end - mb_start + 1;
                    
                    final_events = [final_events; mb_event];
                    final_labels{end+1} = 'MB';
                    final_locations = [final_locations; mb_event.start_time, mb_event.end_time];
                    
                elseif strcmp(event_types{i}, 'SB')
                    % 保留独立的SB事件
                    final_events = [final_events; sorted_sb_events(i)];
                    final_labels{end+1} = 'SB';
                    final_locations = [final_locations; sorted_sb_events(i).start_time, sorted_sb_events(i).end_time];
                    i = i + 1;
                else
                    i = i + 1;
                end
            end
            
            % 【CRS检测逻辑】使用独立阈值检测CRS事件，可以替代SB/MB
            disp(['=== CRS检测：使用独立阈值检测连续性声音 ===']);
            
            % 设置CRS独立阈值
            crs_threshold = signal_mean + crs_alpha * signal_std;
            disp(['CRS检测阈值: ', num2str(crs_threshold), ' (独立于SB阈值)']);
            
            % 找到大于CRS阈值的区域
            above_crs_threshold = I_BowelSound > crs_threshold;
            
            % 找到CRS上升沿和下降沿
            crs_rising_edges = find(diff([0, above_crs_threshold, 0]) == 1);
            crs_falling_edges = find(diff([0, above_crs_threshold, 0]) == -1) - 1;
            
            % 计算3ms对应的样本数（边界延伸）
            crs_extension_samples = round(0.003 * Fs);
            
            % 将每个CRS事件的边界向前后延伸3ms
            crs_extended_rising_edges = max(1, crs_rising_edges - crs_extension_samples);
            crs_extended_falling_edges = min(length(T_C), crs_falling_edges + crs_extension_samples);
            
            % 对所有超过CRS阈值的事件进行检测
            crs_events = [];
            crs_num_events = length(crs_rising_edges);
            
            for i = 1:crs_num_events
                % 计算事件持续时间（毫秒），使用延伸后的边界
                duration_ms = (T_C(crs_extended_falling_edges(i)) - T_C(crs_extended_rising_edges(i))) * 1000;
                
                % CRS检测条件：只使用阈值+时间范围筛选
                if duration_ms >= min_crs_duration_ms && duration_ms <= max_crs_duration_ms
                    % 计算事件的强度特征
                    event_range = crs_rising_edges(i):crs_falling_edges(i);
                    event_avg_intensity = mean(I_BowelSound(event_range));
                    event_peak_intensity = max(I_BowelSound(event_range));
                    
                    % 记录事件的起止时间
                    event_start = T_C(crs_extended_rising_edges(i));
                    event_end = T_C(crs_extended_falling_edges(i));
                    
                    % 创建CRS事件
                    crs_event.start_time = event_start;
                    crs_event.end_time = event_end;
                    crs_event.duration = duration_ms;
                    crs_event.peak_intensity = event_peak_intensity;
                    crs_event.avg_intensity = event_avg_intensity;
                    crs_event.snr = NaN;
                    crs_event.rising_rate = NaN;
                    crs_event.falling_rate = NaN;
                    crs_event.component_count = 1;
                    % crs_event.intensity_cv = NaN;     % 暂时注释
                    % crs_event.max_silence_ms = NaN;   % 暂时注释
                    
                    crs_events = [crs_events; crs_event];
                    
                    fprintf('CRS候选-%d: %.3fs-%.3fs (%.1fms), 峰值=%.3f\n', ...
                        length(crs_events), event_start, event_end, duration_ms, event_peak_intensity);
                end
            end
            
            % 处理CRS与SB/MB的关系：CRS可以替代重叠的SB/MB
            if ~isempty(crs_events)
                disp(['=== CRS替代处理：检查CRS与SB/MB的重叠关系 ===']);
                
                % 标记要被CRS替代的SB/MB事件
                events_to_remove = [];
                overlap_tolerance = 0.01; % 10ms的重叠容差
                
                if ~isempty(final_events)
                    for crs_idx = 1:length(crs_events)
                        crs_start = crs_events(crs_idx).start_time;
                        crs_end = crs_events(crs_idx).end_time;
                        
                        for sb_mb_idx = 1:length(final_events)
                            sb_mb_start = final_events(sb_mb_idx).start_time;
                            sb_mb_end = final_events(sb_mb_idx).end_time;
                            
                            % 检查CRS是否包含或重叠SB/MB事件
                            if (crs_start <= sb_mb_start + overlap_tolerance && crs_end >= sb_mb_end - overlap_tolerance) || ...
                               (crs_start < sb_mb_end - overlap_tolerance && crs_end > sb_mb_start + overlap_tolerance)
                                events_to_remove = [events_to_remove, sb_mb_idx];
                                fprintf('  CRS-%d (%.3fs-%.3fs) 替代 %s-%d (%.3fs-%.3fs)\n', ...
                                    crs_idx, crs_start, crs_end, ...
                                    final_labels{sb_mb_idx}, sb_mb_idx, sb_mb_start, sb_mb_end);
                            end
                        end
                    end
                    
                    % 移除被CRS替代的SB/MB事件
                    if ~isempty(events_to_remove)
                        events_to_remove = unique(events_to_remove);
                        final_events(events_to_remove) = [];
                        final_labels(events_to_remove) = [];
                        final_locations(events_to_remove, :) = [];
                        disp(['  已移除 ', num2str(length(events_to_remove)), ' 个被CRS替代的SB/MB事件']);
                    end
                end
                
                % 将CRS事件添加到最终结果中
                if ~isempty(final_events)
                    % 合并现有事件和CRS事件
                    combined_events = [final_events; crs_events];
                    combined_labels = [final_labels, repmat({'CRS'}, 1, length(crs_events))];
                    combined_locations = [final_locations; [crs_events.start_time]', [crs_events.end_time]'];
                else
                    % 只有CRS事件
                    combined_events = crs_events;
                    combined_labels = repmat({'CRS'}, 1, length(crs_events));
                    combined_locations = [[crs_events.start_time]', [crs_events.end_time]'];
                end
                
                % 按时间排序所有事件
                [~, sort_idx] = sort([combined_events.start_time]);
                final_events = combined_events(sort_idx);
                final_labels = combined_labels(sort_idx);
                final_locations = combined_locations(sort_idx, :);
                
                disp(['=== CRS检测结果 ===']);
                disp(['检测到CRS事件数: ', num2str(length(crs_events))]);
            else
                disp(['=== CRS检测结果 ===']);
                disp(['未检测到符合条件的CRS事件']);
                disp(['要求：超过阈值', num2str(crs_threshold, '%.3f'), '，时间范围', num2str(min_crs_duration_ms), '-', num2str(max_crs_duration_ms), 'ms']);
            end
            
            % 转换为输出格式
            if ~isempty(final_events)
                labelVals = string(final_labels');
                labelLocs = final_locations;
            end
            
            % 显示SB/MB检测结果
            if mb_detected
                mb_count = sum(strcmp(final_labels, 'MB'));
                sb_count = sum(strcmp(final_labels, 'SB'));
                crs_count = sum(strcmp(final_labels, 'CRS'));
                disp(['=== SB/MB/CRS检测结果 ===']);
                disp(['检测到MB事件数: ', num2str(mb_count)]);
                disp(['剩余SB事件数: ', num2str(sb_count)]);
                disp(['检测到CRS事件数: ', num2str(crs_count)]);
                disp(['总事件数: ', num2str(length(final_labels))]);
                
                % 显示每个MB事件的详情
                mb_idx = 1;
                for k = 1:length(final_labels)
                    if strcmp(final_labels{k}, 'MB')
                        fprintf('MB-%d: %.3fs-%.3fs (%.1fms), 包含%d个SB组件, 峰值=%.3f\n', ...
                            mb_idx, final_events(k).start_time, final_events(k).end_time, ...
                            final_events(k).duration, final_events(k).component_count, final_events(k).peak_intensity);
                        mb_idx = mb_idx + 1;
                    end
                end
            else
                sb_count = sum(strcmp(final_labels, 'SB'));
                crs_count = sum(strcmp(final_labels, 'CRS'));
                disp(['=== SB/CRS检测结果 ===']);
                disp(['未检测到符合条件的MB序列']);
                disp(['剩余SB事件数: ', num2str(sb_count)]);
                disp(['检测到CRS事件数: ', num2str(crs_count)]);
                disp(['总事件数: ', num2str(length(final_labels))]);
                disp(['MB要求：', num2str(min_sb_count), '-', num2str(max_sb_count), '个SB，间隙', num2str(min_gap_ms), '-', num2str(max_gap_ms), 'ms，总时长', num2str(min_mb_duration_ms), '-', num2str(max_mb_duration_ms), 'ms']);
                
                % 如果没有MB，但可能有CRS，保持更新后的结果
                if isempty(final_events)
                    labelVals = repmat("SB", length(sb_events), 1);
                    labelLocs = [[sb_events.start_time]', [sb_events.end_time]'];
                end
            end
        else
            labelVals = string([]);
            labelLocs = zeros(0,2);
        end

        % 显示最终肠鸣音检测结果（支持SB、MB和CRS）
        if ~isempty(labelVals)
            disp(['=== 肠鸣音检测最终结果（SB+MB+CRS检测）===']);
            
            % 统计各类型事件数量
            sb_count = sum(labelVals == "SB");
            mb_count = sum(labelVals == "MB");
            crs_count = sum(labelVals == "CRS");
            total_count = length(labelVals);
            
            disp(['检测结果: ', num2str(sb_count), '个SB + ', num2str(mb_count), '个MB + ', num2str(crs_count), '个CRS = ', num2str(total_count), '个肠鸣音事件']);
            
            % 简化统计信息
            if sb_count > 0
                sb_indices = find(labelVals == "SB");
                sb_final_events = final_events(sb_indices);
                sb_durations = [sb_final_events.duration];
                disp(['SB: 平均时长', num2str(mean(sb_durations), '%.1f'), 'ms']);
            end
            
            if mb_count > 0
                mb_indices = find(labelVals == "MB");
                mb_final_events = final_events(mb_indices);
                mb_durations = [mb_final_events.duration];
                mb_component_counts = [mb_final_events.component_count];
                disp(['MB: 平均时长', num2str(mean(mb_durations), '%.1f'), 'ms, 平均', num2str(mean(mb_component_counts), '%.1f'), '个SB组件']);
            end
            
            if crs_count > 0
                crs_indices = find(labelVals == "CRS");
                crs_final_events = final_events(crs_indices);
                crs_durations = [crs_final_events.duration];
                crs_peak_intensities = [crs_final_events.peak_intensity];
                disp(['CRS: 平均时长', num2str(mean(crs_durations), '%.1f'), 'ms, 平均峰值强度', num2str(mean(crs_peak_intensities), '%.3f')]);
            end
            
            % 显示每个检测到的事件详情
            disp(['=== 检测到的肠鸣音事件详情 ===']);
            sb_idx = 1;
            mb_idx = 1;
            crs_idx = 1;
            for i = 1:length(labelVals)
                if labelVals(i) == "SB"
                    fprintf('SB-%d: %.3fs-%.3fs (%.1fms)\n', ...
                        sb_idx, final_events(i).start_time, final_events(i).end_time, ...
                        final_events(i).duration);
                    sb_idx = sb_idx + 1;
                elseif labelVals(i) == "MB"
                    fprintf('MB-%d: %.3fs-%.3fs (%.1fms), 包含%d个SB\n', ...
                        mb_idx, final_events(i).start_time, final_events(i).end_time, ...
                        final_events(i).duration, final_events(i).component_count);
                    mb_idx = mb_idx + 1;
                elseif labelVals(i) == "CRS"
                    fprintf('CRS-%d: %.3fs-%.3fs (%.1fms), 峰值=%.3f\n', ...
                        crs_idx, final_events(i).start_time, final_events(i).end_time, ...
                        final_events(i).duration, final_events(i).peak_intensity);
                    crs_idx = crs_idx + 1;
                end
            end
        else
            disp(['检测结果: 未检测到符合条件的肠鸣音事件（SB、MB或CRS）']);
        end
    end

    % 如果没有检测到任何SB事件，确保输出为空
    if isempty(labelVals)
        labelVals = string([]);
        labelLocs = zeros(0,2);
    end
    
    % 验证输出格式（调试用）
    if ~isempty(labelVals)
        disp(['=== 输出验证 ===']);
        disp(['标签数组大小: ', num2str(size(labelVals))]);
        disp(['位置矩阵大小: ', num2str(size(labelLocs))]);
        disp(['标签类型: ', char(strjoin(unique(labelVals), ', '))]);
        
        % 验证数据一致性
        if length(labelVals) ~= size(labelLocs, 1)
            error('标签数量与位置数量不匹配！');
        end
        
        % 验证时间范围
        if any(labelLocs(:,2) <= labelLocs(:,1))
            error('存在无效的时间范围！');
        end
        
        disp('✓ 输出格式验证通过');
    end
end


% % 【注释掉的原始代码】HS (谐波音) 检测
% % for i = 1:num_events
% %     if strcmp(event_types{i}, 'SB') || isempty(event_types{i}) || strcmp(event_types{i}, '')
% %         % 对未分类或可疑事件进行谐波分析
% %         event_range = rising_edges(i):falling_edges(i);
% %         duration_ms = (T_C(falling_edges(i)) - T_C(rising_edges(i))) * 1000;
% %         
% %         % HS持续时间检查
% %         if duration_ms >= 50 && duration_ms <= 1500
% %             % 分析该事件的频谱特征
% %             event_spectrum = mean(P_C(:, event_range), 2);
% %             
% %             % 寻找频谱峰值
% %             [peaks, peak_locs] = findpeaks(event_spectrum, 'MinPeakHeight', max(event_spectrum) * 0.15, ...
% %                                          'MinPeakDistance', round(0.05 * length(event_spectrum)));
% %             
% %             % 将峰值位置转换为频率
% %             if ~isempty(peaks)
% %                 peak_frequencies = F_C(peak_locs);
% %                 
% %                 % 过滤100-800Hz范围内的峰值
% %                 valid_peaks = peak_frequencies >= 100 & peak_frequencies <= 800;
% %                 peak_frequencies = peak_frequencies(valid_peaks);
% %                 peaks = peaks(valid_peaks);
% %                 
% %                 % HS判断：需要3-4个清晰峰值
% %                 if length(peak_frequencies) >= 3 && length(peak_frequencies) <= 4
% %                     % 检查是否为谐波关系
% %                     [peaks_sorted, sort_idx] = sort(peaks, 'descend');
% %                     freq_sorted = peak_frequencies(sort_idx);
% %                     
% %                     % 假设最强峰值为基频或其谐波
% %                     best_fundamental = 0;
% %                     max_harmonic_matches = 0;
% %                     
% %                     for base_freq = 100:5:300  % 搜索可能的基频
% %                         harmonic_matches = 0;
% %                         for freq = freq_sorted
% %                             % 检查是否为基频的整数倍（允许8%误差）
% %                             harmonic_ratio = freq / base_freq;
% %                             if abs(harmonic_ratio - round(harmonic_ratio)) < 0.08
% %                                 harmonic_matches = harmonic_matches + 1;
% %                             end
% %                         end
% %                         
% %                         if harmonic_matches > max_harmonic_matches
% %                             max_harmonic_matches = harmonic_matches;
% %                             best_fundamental = base_freq;
% %                         end
% %                     end
% %                     
% %                     % 如果找到良好的谐波关系
% %                     if max_harmonic_matches >= 3
% %                         event_types{i} = 'HS';
% %                         disp(['检测到HS事件 ', num2str(i), ':']);
% %                         disp(['  持续时间: ', num2str(duration_ms), ' ms']);
% %                         disp(['  估计基频: ', num2str(best_fundamental), ' Hz']);
% %                         disp(['  谐波数: ', num2str(max_harmonic_matches)]);
% %                     end
% %                 end
% %             end
% %         end
% %     end
% % end






















